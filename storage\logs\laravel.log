[2025-07-05 15:34:36] local.ERROR: The C:\Users\<USER>\Documents\BarnaTrasteros_1.0\bootstrap\cache directory must be present and writable. {"exception":"[object] (Exception(code: 0): The C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\bootstrap\\cache directory must be present and writable. at C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php:177)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(130): Illuminate\\Foundation\\PackageManifest->write()
#1 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(106): Illuminate\\Foundation\\PackageManifest->build()
#2 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(89): Illuminate\\Foundation\\PackageManifest->getManifest()
#3 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(78): Illuminate\\Foundation\\PackageManifest->config()
#4 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#5 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(237): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap()
#6 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(310): Illuminate\\Foundation\\Application->bootstrapWith()
#7 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(127): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#8 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#9 {main}
"} 
[2025-07-05 15:44:08] local.ERROR: The C:\Users\<USER>\Documents\BarnaTrasteros_1.0\bootstrap\cache directory must be present and writable. {"exception":"[object] (Exception(code: 0): The C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\bootstrap\\cache directory must be present and writable. at C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php:177)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(130): Illuminate\\Foundation\\PackageManifest->write()
#1 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(106): Illuminate\\Foundation\\PackageManifest->build()
#2 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(89): Illuminate\\Foundation\\PackageManifest->getManifest()
#3 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(78): Illuminate\\Foundation\\PackageManifest->config()
#4 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#5 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(237): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap()
#6 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(310): Illuminate\\Foundation\\Application->bootstrapWith()
#7 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(127): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#8 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#9 {main}
"} 
[2025-07-05 16:21:31] local.ERROR: The C:\Users\<USER>\Documents\BarnaTrasteros_1.0\bootstrap\cache directory must be present and writable. {"exception":"[object] (Exception(code: 0): The C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\bootstrap\\cache directory must be present and writable. at C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php:179)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(132): Illuminate\\Foundation\\PackageManifest->write()
#1 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(108): Illuminate\\Foundation\\PackageManifest->build()
#2 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(90): Illuminate\\Foundation\\PackageManifest->getManifest()
#3 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(79): Illuminate\\Foundation\\PackageManifest->config()
#4 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#5 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap()
#6 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith()
#7 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#8 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#9 {main}
"} 
[2025-07-05 16:22:51] local.ERROR: The C:\Users\<USER>\Documents\BarnaTrasteros_1.0\bootstrap\cache directory must be present and writable. {"exception":"[object] (Exception(code: 0): The C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\bootstrap\\cache directory must be present and writable. at C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php:179)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(132): Illuminate\\Foundation\\PackageManifest->write()
#1 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(108): Illuminate\\Foundation\\PackageManifest->build()
#2 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(90): Illuminate\\Foundation\\PackageManifest->getManifest()
#3 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(79): Illuminate\\Foundation\\PackageManifest->config()
#4 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#5 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap()
#6 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith()
#7 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#8 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#9 {main}
"} 
[2025-07-05 16:22:56] local.ERROR: The C:\Users\<USER>\Documents\BarnaTrasteros_1.0\bootstrap\cache directory must be present and writable. {"exception":"[object] (Exception(code: 0): The C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\bootstrap\\cache directory must be present and writable. at C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php:179)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(132): Illuminate\\Foundation\\PackageManifest->write()
#1 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(108): Illuminate\\Foundation\\PackageManifest->build()
#2 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(90): Illuminate\\Foundation\\PackageManifest->getManifest()
#3 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(79): Illuminate\\Foundation\\PackageManifest->config()
#4 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#5 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap()
#6 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith()
#7 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#8 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#9 {main}
"} 
[2025-07-05 16:23:46] local.ERROR: The C:\Users\<USER>\Documents\BarnaTrasteros_1.0\bootstrap\cache directory must be present and writable. {"exception":"[object] (Exception(code: 0): The C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\bootstrap\\cache directory must be present and writable. at C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php:179)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(132): Illuminate\\Foundation\\PackageManifest->write()
#1 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(108): Illuminate\\Foundation\\PackageManifest->build()
#2 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(90): Illuminate\\Foundation\\PackageManifest->getManifest()
#3 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(79): Illuminate\\Foundation\\PackageManifest->config()
#4 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#5 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap()
#6 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith()
#7 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#8 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#9 {main}
"} 
[2025-07-05 16:24:57] local.ERROR: The C:\Users\<USER>\Documents\BarnaTrasteros_1.0\bootstrap\cache directory must be present and writable. {"exception":"[object] (Exception(code: 0): The C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\bootstrap\\cache directory must be present and writable. at C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php:179)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(132): Illuminate\\Foundation\\PackageManifest->write()
#1 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(108): Illuminate\\Foundation\\PackageManifest->build()
#2 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(90): Illuminate\\Foundation\\PackageManifest->getManifest()
#3 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(79): Illuminate\\Foundation\\PackageManifest->config()
#4 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#5 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap()
#6 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith()
#7 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#8 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#9 {main}
"} 
[2025-07-05 16:25:36] local.ERROR: In order to use the Auth::routes() method, please install the laravel/ui package. {"exception":"[object] (RuntimeException(code: 0): In order to use the Auth::routes() method, please install the laravel/ui package. at C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Auth.php:93)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\routes\\web.php(5): Illuminate\\Support\\Facades\\Auth::routes()
#1 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteFileRegistrar.php(34): require('...')
#2 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(512): Illuminate\\Routing\\RouteFileRegistrar->register()
#3 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(466): Illuminate\\Routing\\Router->loadRoutes()
#4 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(206): Illuminate\\Routing\\Router->group()
#5 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Configuration\\ApplicationBuilder.php(248): Illuminate\\Routing\\RouteRegistrar->group()
#6 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Configuration\\ApplicationBuilder->Illuminate\\Foundation\\Configuration\\{closure}()
#7 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#8 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure()
#9 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#10 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#11 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(162): Illuminate\\Container\\Container->call()
#12 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(59): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->loadRoutes()
#13 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->Illuminate\\Foundation\\Support\\Providers\\{closure}()
#14 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#15 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure()
#16 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#17 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#18 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(143): Illuminate\\Container\\Container->call()
#19 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1153): Illuminate\\Support\\ServiceProvider->callBootedCallbacks()
#20 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1131): Illuminate\\Foundation\\Application->bootProvider()
#21 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}()
#22 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1130): array_walk()
#23 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#24 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap()
#25 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith()
#26 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#27 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#28 {main}
"} 
[2025-07-05 16:28:09] local.ERROR: Cannot declare class CreateAlquileresTable2, because the name is already in use {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Cannot declare class CreateAlquileresTable2, because the name is already in use at C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\database\\migrations\\2021_11_28_145121_create_alquileres_table.php:7)
[stacktrace]
#0 {main}
"} 
[2025-07-05 16:28:33] local.ERROR: Cannot declare class CreateAlquileresTable2, because the name is already in use {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Cannot declare class CreateAlquileresTable2, because the name is already in use at C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\database\\migrations\\2021_11_28_145121_create_alquileres_table.php:7)
[stacktrace]
#0 {main}
"} 
[2025-07-05 16:29:26] local.ERROR: Cannot declare class CreateAlquileresTableOld, because the name is already in use {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Cannot declare class CreateAlquileresTableOld, because the name is already in use at C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\database\\migrations\\2021_11_28_145121_create_alquileres_table.php:7)
[stacktrace]
#0 {main}
"} 
[2025-07-05 16:30:12] local.ERROR: Cannot declare class CreateAlquileresTableOld, because the name is already in use {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Cannot declare class CreateAlquileresTableOld, because the name is already in use at C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\database\\migrations\\2021_11_28_145121_create_alquileres_table.php:7)
[stacktrace]
#0 {main}
"} 
[2025-07-05 16:36:58] local.ERROR: file_put_contents(C:\Users\<USER>\Documents\BarnaTrasteros_1.0\storage\framework/sessions/JcVBL4UDEglvdsOtCSrewfwmOiHQ3wE2IIaaxeTL): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): file_put_contents(C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\storage\\framework/sessions/JcVBL4UDEglvdsOtCSrewfwmOiHQ3wE2IIaaxeTL): Failed to open stream: No such file or directory at C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php:204)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError()
#1 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}()
#2 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(204): file_put_contents()
#3 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\FileSessionHandler.php(89): Illuminate\\Filesystem\\Filesystem->put()
#4 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(175): Illuminate\\Session\\FileSessionHandler->write()
#5 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(244): Illuminate\\Session\\Store->save()
#6 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(129): Illuminate\\Session\\Middleware\\StartSession->saveSession()
#7 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#8 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle()
#9 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#10 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#11 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#12 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#13 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#14 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#15 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#16 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#17 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#18 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#19 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#20 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#21 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#22 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#23 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#24 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#25 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#26 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#27 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#28 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#29 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#30 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#31 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle()
#32 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#33 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#34 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#35 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#36 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#37 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#38 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#39 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#40 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#41 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle()
#42 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#43 {main}
"} 
[2025-07-05 16:40:54] local.ERROR: The "--compact" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"--compact\" option does not exist. at C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\symfony\\console\\Input\\ArgvInput.php:226)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\symfony\\console\\Input\\ArgvInput.php(155): Symfony\\Component\\Console\\Input\\ArgvInput->addLongOption()
#1 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\symfony\\console\\Input\\ArgvInput.php(88): Symfony\\Component\\Console\\Input\\ArgvInput->parseLongOption()
#2 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\symfony\\console\\Input\\ArgvInput.php(77): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken()
#3 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\symfony\\console\\Input\\Input.php(53): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\symfony\\console\\Command\\Command.php(276): Symfony\\Component\\Console\\Input\\Input->bind()
#5 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run()
#6 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run()
#7 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand()
#8 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun()
#9 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#10 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#11 {main}
"} 
[2025-07-05 16:41:01] local.ERROR: Class "App\Http\Controllers\ClientesController" does not exist {"exception":"[object] (ReflectionException(code: -1): Class \"App\\Http\\Controllers\\ClientesController\" does not exist at C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php:235)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(235): ReflectionClass->__construct()
#1 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(149): Illuminate\\Foundation\\Console\\RouteListCommand->isVendorRoute()
#2 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(117): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation()
#3 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}()
#4 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(749): array_map()
#5 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(809): Illuminate\\Support\\Arr::map()
#6 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(117): Illuminate\\Support\\Collection->map()
#7 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(102): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#8 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#9 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#10 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#11 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#12 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#13 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call()
#14 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute()
#15 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run()
#16 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run()
#17 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand()
#18 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun()
#19 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#20 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#21 {main}
"} 
[2025-07-05 16:41:37] local.ERROR: Trait "App\Http\Controllers\AuthenticatesUsers" not found {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Trait \"App\\Http\\Controllers\\AuthenticatesUsers\" not found at C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\app\\Http\\Controllers\\PrecioController.php:9)
[stacktrace]
#0 {main}
"} 
[2025-07-05 16:45:42] local.ERROR: Class "App\Http\Controllers\RouteServiceProvider" not found {"exception":"[object] (Error(code: 0): Class \"App\\Http\\Controllers\\RouteServiceProvider\" not found at C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\app\\Http\\Controllers\\HomeController.php:15)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1062): [constant expression]()
#1 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1062): ReflectionClass->newInstanceArgs()
#2 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build()
#3 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve()
#4 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve()
#5 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make()
#6 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(286): Illuminate\\Foundation\\Application->make()
#7 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1127): Illuminate\\Routing\\Route->getController()
#8 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1056): Illuminate\\Routing\\Route->controllerMiddleware()
#9 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(820): Illuminate\\Routing\\Route->gatherMiddleware()
#10 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(211): Illuminate\\Routing\\Router->gatherRouteMiddleware()
#11 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(148): Illuminate\\Foundation\\Console\\RouteListCommand->getMiddleware()
#12 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(117): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation()
#13 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}()
#14 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(749): array_map()
#15 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(809): Illuminate\\Support\\Arr::map()
#16 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(117): Illuminate\\Support\\Collection->map()
#17 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(102): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#18 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#19 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#20 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#21 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#22 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#23 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call()
#24 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute()
#25 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run()
#26 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run()
#27 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand()
#28 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun()
#29 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#30 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#31 {main}
"} 
[2025-07-05 17:14:34] local.ERROR: Division by zero {"userId":2,"exception":"[object] (DivisionByZeroError(code: 0): Division by zero at C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\app\\Http\\Controllers\\HomeController.php:61)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\HomeController->index()
#1 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#2 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#3 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#4 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#5 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#6 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#7 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#8 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#9 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#10 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#11 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#12 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#13 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#14 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#15 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#16 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle()
#17 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#18 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#19 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#20 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#21 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#22 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#23 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#24 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#25 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#26 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#27 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#28 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#29 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#30 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#31 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#32 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#33 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#34 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#35 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#36 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#37 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#38 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#39 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle()
#40 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#41 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#42 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#43 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#44 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#45 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#46 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#47 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#48 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#49 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle()
#50 C:\\Users\\<USER>\\Documents\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#51 {main}
"} 
[2025-07-07 10:36:48] local.ERROR: The C:\Users\<USER>\Documents\___CodeS\BarnaTrasteros_1.0\bootstrap\cache directory must be present and writable. {"exception":"[object] (Exception(code: 0): The C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\bootstrap\\cache directory must be present and writable. at C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php:179)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(132): Illuminate\\Foundation\\PackageManifest->write()
#1 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(108): Illuminate\\Foundation\\PackageManifest->build()
#2 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(90): Illuminate\\Foundation\\PackageManifest->getManifest()
#3 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(79): Illuminate\\Foundation\\PackageManifest->config()
#4 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#5 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap()
#6 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith()
#7 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#8 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#9 {main}
"} 
[2025-07-07 10:37:03] local.ERROR: The C:\Users\<USER>\Documents\___CodeS\BarnaTrasteros_1.0\bootstrap\cache directory must be present and writable. {"exception":"[object] (Exception(code: 0): The C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\bootstrap\\cache directory must be present and writable. at C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php:179)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(132): Illuminate\\Foundation\\PackageManifest->write()
#1 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(108): Illuminate\\Foundation\\PackageManifest->build()
#2 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(90): Illuminate\\Foundation\\PackageManifest->getManifest()
#3 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(79): Illuminate\\Foundation\\PackageManifest->config()
#4 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#5 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap()
#6 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(186): Illuminate\\Foundation\\Application->bootstrapWith()
#7 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(170): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#8 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#9 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle()
#10 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#11 {main}
"} 
[2025-07-07 10:38:51] local.ERROR: The C:\Users\<USER>\Documents\___CodeS\BarnaTrasteros_1.0\bootstrap\cache directory must be present and writable. {"exception":"[object] (Exception(code: 0): The C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\bootstrap\\cache directory must be present and writable. at C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php:179)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(132): Illuminate\\Foundation\\PackageManifest->write()
#1 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(108): Illuminate\\Foundation\\PackageManifest->build()
#2 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(90): Illuminate\\Foundation\\PackageManifest->getManifest()
#3 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(79): Illuminate\\Foundation\\PackageManifest->config()
#4 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#5 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap()
#6 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(186): Illuminate\\Foundation\\Application->bootstrapWith()
#7 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(170): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#8 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#9 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle()
#10 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#11 {main}
"} 
[2025-07-07 10:38:53] local.ERROR: The C:\Users\<USER>\Documents\___CodeS\BarnaTrasteros_1.0\bootstrap\cache directory must be present and writable. {"exception":"[object] (Exception(code: 0): The C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\bootstrap\\cache directory must be present and writable. at C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php:179)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(132): Illuminate\\Foundation\\PackageManifest->write()
#1 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(108): Illuminate\\Foundation\\PackageManifest->build()
#2 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(90): Illuminate\\Foundation\\PackageManifest->getManifest()
#3 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(79): Illuminate\\Foundation\\PackageManifest->config()
#4 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#5 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap()
#6 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith()
#7 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#8 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#9 {main}
"} 
[2025-07-07 10:40:14] local.ERROR: The C:\Users\<USER>\Documents\___CodeS\BarnaTrasteros_1.0\bootstrap\cache directory must be present and writable. {"exception":"[object] (Exception(code: 0): The C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\bootstrap\\cache directory must be present and writable. at C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php:179)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(132): Illuminate\\Foundation\\PackageManifest->write()
#1 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(108): Illuminate\\Foundation\\PackageManifest->build()
#2 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(90): Illuminate\\Foundation\\PackageManifest->getManifest()
#3 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(79): Illuminate\\Foundation\\PackageManifest->config()
#4 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#5 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap()
#6 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith()
#7 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#8 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#9 {main}
"} 
[2025-07-07 10:41:49] local.ERROR: The C:\Users\<USER>\Documents\___CodeS\BarnaTrasteros_1.0\bootstrap\cache directory must be present and writable. {"exception":"[object] (Exception(code: 0): The C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\bootstrap\\cache directory must be present and writable. at C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php:179)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(132): Illuminate\\Foundation\\PackageManifest->write()
#1 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(108): Illuminate\\Foundation\\PackageManifest->build()
#2 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(90): Illuminate\\Foundation\\PackageManifest->getManifest()
#3 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(79): Illuminate\\Foundation\\PackageManifest->config()
#4 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#5 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap()
#6 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith()
#7 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#8 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#9 {main}
"} 
[2025-07-07 10:42:08] local.ERROR: The C:\Users\<USER>\Documents\___CodeS\BarnaTrasteros_1.0\bootstrap\cache directory must be present and writable. {"exception":"[object] (Exception(code: 0): The C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\bootstrap\\cache directory must be present and writable. at C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php:179)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(132): Illuminate\\Foundation\\PackageManifest->write()
#1 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(108): Illuminate\\Foundation\\PackageManifest->build()
#2 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(90): Illuminate\\Foundation\\PackageManifest->getManifest()
#3 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(79): Illuminate\\Foundation\\PackageManifest->config()
#4 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#5 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap()
#6 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith()
#7 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#8 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#9 {main}
"} 
[2025-07-07 11:17:49] local.ERROR: The C:\Users\<USER>\Documents\___CodeS\BarnaTrasteros_1.0\bootstrap\cache directory must be present and writable. {"exception":"[object] (Exception(code: 0): The C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\bootstrap\\cache directory must be present and writable. at C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php:179)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(132): Illuminate\\Foundation\\PackageManifest->write()
#1 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(108): Illuminate\\Foundation\\PackageManifest->build()
#2 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(90): Illuminate\\Foundation\\PackageManifest->getManifest()
#3 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(79): Illuminate\\Foundation\\PackageManifest->config()
#4 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#5 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap()
#6 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith()
#7 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#8 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#9 {main}
"} 
[2025-07-07 11:17:59] local.ERROR: The C:\Users\<USER>\Documents\___CodeS\BarnaTrasteros_1.0\bootstrap\cache directory must be present and writable. {"exception":"[object] (Exception(code: 0): The C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\bootstrap\\cache directory must be present and writable. at C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php:179)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(132): Illuminate\\Foundation\\PackageManifest->write()
#1 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(108): Illuminate\\Foundation\\PackageManifest->build()
#2 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(90): Illuminate\\Foundation\\PackageManifest->getManifest()
#3 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(79): Illuminate\\Foundation\\PackageManifest->config()
#4 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#5 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap()
#6 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith()
#7 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#8 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#9 {main}
"} 
[2025-07-07 11:18:32] local.ERROR: The C:\Users\<USER>\Documents\___CodeS\BarnaTrasteros_1.0\bootstrap\cache directory must be present and writable. {"exception":"[object] (Exception(code: 0): The C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\bootstrap\\cache directory must be present and writable. at C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php:179)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(132): Illuminate\\Foundation\\PackageManifest->write()
#1 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(108): Illuminate\\Foundation\\PackageManifest->build()
#2 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(90): Illuminate\\Foundation\\PackageManifest->getManifest()
#3 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(79): Illuminate\\Foundation\\PackageManifest->config()
#4 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#5 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap()
#6 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith()
#7 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#8 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#9 {main}
"} 
[2025-07-07 11:26:38] local.ERROR: Command "session:flush" is not defined.

Did you mean one of these?
    queue:flush
    session:table {"exception":"[object] (Symfony\\Component\\Console\\Exception\\CommandNotFoundException(code: 0): Command \"session:flush\" is not defined.

Did you mean one of these?
    queue:flush
    session:table at C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\symfony\\console\\Application.php:725)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\symfony\\console\\Application.php(283): Symfony\\Component\\Console\\Application->find()
#1 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun()
#2 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#3 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#4 {main}
"} 
